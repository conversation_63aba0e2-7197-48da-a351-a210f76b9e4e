"""
Core module for GretahAI ScriptWeaver.

This module provides core functionality for the application including:
- UI element detection and analysis
- Test case analysis and mapping
- AI integration for script generation
- Element matching and selection

The module is organized into several submodules:
- elements.py: UI element detection and filtering
- analysis.py: Test case analysis and element mapping
- ai.py: AI integration and script generation
- element_detection.py: Advanced element detection
- element_matching.py: AI-powered element matching
- interactive_selector.py: Interactive element selection
- navigation_helpers.py: Stage navigation and validation
- performance_monitor.py: Performance tracking
- script_storage.py: Script persistence
- step_data_storage.py: Step data management

© 2025 Cogniron All Rights Reserved.
"""

import os
import sys
import logging
import traceback

# Configure logging
logger = logging.getLogger("ScriptWeaver.core")

# Add the parent directory to the path so we can import from parent modules
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

def _safe_import(module_name, function_names, fallback_functions=None):
    """
    Safely import functions from a module with fallback handling.

    Args:
        module_name (str): Name of the module to import from
        function_names (list): List of function names to import
        fallback_functions (dict): Optional fallback functions

    Returns:
        dict: Dictionary of imported or fallback functions
    """
    imported_functions = {}

    try:
        module = __import__(f".{module_name}", package=__name__, fromlist=function_names)
        for func_name in function_names:
            if hasattr(module, func_name):
                imported_functions[func_name] = getattr(module, func_name)
                logger.debug(f"Successfully imported {func_name} from {module_name}")
            else:
                logger.warning(f"Function {func_name} not found in {module_name}")
                if fallback_functions and func_name in fallback_functions:
                    imported_functions[func_name] = fallback_functions[func_name]
    except ImportError as e:
        logger.error(f"Error importing from {module_name}: {e}")
        logger.debug(f"Traceback: {traceback.format_exc()}")

        # Use fallback functions if available
        if fallback_functions:
            for func_name in function_names:
                if func_name in fallback_functions:
                    imported_functions[func_name] = fallback_functions[func_name]
                    logger.info(f"Using fallback function for {func_name}")

    return imported_functions

# Define fallback functions for critical functionality
def _create_fallback_functions():
    """Create fallback functions for when imports fail."""

    def detect_elements_fallback(url, browser=None, headless=True, save_to_file=True, elements_dir="detected_elements"):
        """Fallback detect_elements function."""
        logger.error(f"Cannot detect UI elements on {url}: detect_elements function not available")
        return []

    def find_ui_elements_fallback(url, browser=None):
        """Fallback find_ui_elements function."""
        logger.error(f"Cannot find UI elements on {url}: find_ui_elements function not available")
        return []

    def map_test_steps_to_elements_fallback(test_steps, ui_elements):
        """Fallback map_test_steps_to_elements function."""
        logger.error("Cannot map test steps to elements: function not available")
        return {}

    def analyze_test_case_fallback(test_case):
        """Fallback analyze_test_case function."""
        logger.error("Cannot analyze test case: function not available")
        return {"analysis": "Function not available", "valid": False}

    def filter_qa_relevant_elements_fallback(elements):
        """Fallback filter_qa_relevant_elements function."""
        logger.error("Cannot filter QA relevant elements: function not available")
        return elements[:20] if elements else []

    def initialize_ai_client_fallback(api_key=None):
        """Fallback initialize_ai_client function."""
        logger.error("Cannot initialize AI client: function not available")
        return False

    def generate_llm_response_fallback(prompt, model_name=None, **kwargs):
        """Fallback generate_llm_response function."""
        logger.error("Cannot generate LLM response: function not available")
        return ""

    def convert_test_case_to_step_table_fallback(test_case, api_key=None):
        """Fallback convert_test_case_to_step_table function."""
        logger.error("Cannot convert test case to step table: function not available")
        return "Error: Function not available", []

    def generate_test_script_fallback(step_table, test_data, api_key=None):
        """Fallback generate_test_script function."""
        logger.error("Cannot generate test script: function not available")
        return "Error: Function not available", "Error: Function not available"

    def match_elements_with_ai_fallback(test_case, elements, api_key=None):
        """Fallback match_elements_with_ai function."""
        logger.error("Cannot match elements with AI: function not available")
        return {}

    def select_element_interactively_fallback(url, browser=None, headless=False):
        """Fallback select_element_interactively function."""
        logger.error("Cannot select element interactively: function not available")
        return None

    return {
        'detect_elements': detect_elements_fallback,
        'find_ui_elements': find_ui_elements_fallback,
        'map_test_steps_to_elements': map_test_steps_to_elements_fallback,
        'analyze_test_case': analyze_test_case_fallback,
        'filter_qa_relevant_elements': filter_qa_relevant_elements_fallback,
        'initialize_ai_client': initialize_ai_client_fallback,
        'generate_llm_response': generate_llm_response_fallback,
        'convert_test_case_to_step_table': convert_test_case_to_step_table_fallback,
        'generate_test_script': generate_test_script_fallback,
        'match_elements_with_ai': match_elements_with_ai_fallback,
        'select_element_interactively': select_element_interactively_fallback,
    }

# Get fallback functions
fallback_functions = _create_fallback_functions()

# Import functions from various modules
logger.info("Importing core module functions...")

# Import from elements.py
elements_functions = _safe_import('elements', ['find_ui_elements'], fallback_functions)

# Import from analysis.py
analysis_functions = _safe_import('analysis', [
    'map_test_steps_to_elements',
    'analyze_test_case',
    'filter_qa_relevant_elements',
    'create_element_matching_prompt_with_context'
], fallback_functions)

# Import from ai.py
ai_functions = _safe_import('ai', [
    'initialize_ai_client',
    'generate_llm_response',
    'convert_test_case_to_step_table',
    'generate_test_script',
    'generate_test_script_prompt'
], fallback_functions)

# Import from element_detection.py (if available)
element_detection_functions = _safe_import('element_detection', [
    'detect_elements',
    'detect_elements_advanced',
    'select_element_interactively'
], fallback_functions)

# Import from element_matching.py (if available)
element_matching_functions = _safe_import('element_matching', [
    'match_elements_with_ai',
    'create_element_matching_prompt'
], fallback_functions)

# Import from match_elements.py (legacy support)
match_elements_functions = _safe_import('match_elements', [
    'match_elements_with_ai'
], fallback_functions)

# Import from interactive_selector.py (if available)
interactive_selector_functions = _safe_import('interactive_selector', [
    'select_element_interactively',
    'render_interactive_element_selector',
    'handle_element_selection'
], fallback_functions)

# Combine all imported functions into module namespace
all_functions = {}
all_functions.update(elements_functions)
all_functions.update(analysis_functions)
all_functions.update(ai_functions)
all_functions.update(element_detection_functions)
all_functions.update(element_matching_functions)
all_functions.update(match_elements_functions)
all_functions.update(interactive_selector_functions)

# Add functions to module namespace
globals().update(all_functions)

# Export all functions for easy importing
__all__ = list(all_functions.keys())
